# Trivy ignore file for known false positives and acceptable risks

# CVEs that are not applicable to our use case or have no fix available
# Add specific CVE IDs here as needed, for example:
# CVE-2023-12345

# Ignore vulnerabilities in test dependencies
# These are not included in production builds
**/test/**

# Ignore vulnerabilities in build tools that don't affect runtime
**/maven-wrapper/**
**/mvnw
**/mvnw.cmd

# Alpine base image known issues that are acceptable
# (Add specific CVEs here if needed after reviewing them)

# Example of ignoring a specific vulnerability:
# CVE-2023-5678  # Description: Not applicable to our use case because...
