spring:
  cloud:
    gateway:
      routes:
        - id: config-service-search
          uri: http://localhost:8080
          predicates:
            - Path=/api/v1/config/search/**
          filters:
            - StripPrefix=3
            - NoSQLSearchFilter
        - id: config-service-upsert
          uri: http://localhost:8080
          predicates:
            - Path=/api/v1/config/upsert/**
          filters:
            - StripPrefix=3
            - UpsertFilter
        - id: config-service
          uri: http://localhost:8080
          predicates:
            - Path=/api/v1/config/**
          filters:
            - RewritePath=/api/v1/config/(?<segment>.*), /$\{segment}
        - id: form-builder-service-search
          uri: http://localhost:8081
          predicates:
            - Path=/api/v1/form-builder/search/**
          filters:
            - StripPrefix=3
            - NoSQLSearchFilter
        - id: form-builder-service-upsert
          uri: http://localhost:8081
          predicates:
            - Path=/api/v1/form-builder/upsert/**
          filters:
            - StripPrefix=3
            - UpsertFilter
        - id: form-builder-service
          uri: http://localhost:8081
          predicates:
            - Path=/api/v1/form-builder/**
          filters:
            - RewritePath=/api/v1/form-builder/(?<segment>.*), /$\{segment}
        - id: activity-service-search
          uri: http://localhost:8082
          predicates:
            - Path=/api/v1/activity/search/**
          filters:
            - StripPrefix=3
            - SearchFilter
        - id: activity-service-upsert
          uri: http://localhost:8082
          predicates:
            - Path=/api/v1/activity/upsert/**
          filters:
            - StripPrefix=3
            - UpsertFilter
        - id: activity-service
          uri: http://localhost:8082
          predicates:
            - Path=/api/v1/activity/**
          filters:
            - RewritePath=/api/v1/activity/(?<segment>.*), /$\{segment}
        - id: entity-service-search
          uri: http://localhost:8083
          predicates:
            - Path=/api/v1/entity/search/**
          filters:
            - StripPrefix=3
            - NoSQLSearchFilter
        - id: entity-service-upsert
          uri: http://localhost:8083
          predicates:
            - Path=/api/v1/entity/upsert/**
          filters:
            - StripPrefix=3
            - UpsertFilter
        - id: entity-service
          uri: http://localhost:8083
          predicates:
            - Path=/api/v1/entity/**
          filters:
            - RewritePath=/api/v1/entity/(?<segment>.*), /$\{segment}
        - id: feedback-service
          uri: http://localhost:8084
          predicates:
            - Path=/api/v1/feedback/**
          filters:
            - RewritePath=/api/v1/feedback/(?<segment>.*), /$\{segment}

management:
  endpoints:
    web:
      exposure:
        include: "*"

app:
  auth:
    url: http://localhost:8085

server:
  port: 8082
