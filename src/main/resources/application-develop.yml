spring:
  cloud:
    gateway:
      routes:
        - id: config-service-search
          uri: ${CONFIG_SERVICE_URL}
          predicates:
            - Path=/api/v1/config/search/**
          filters:
            - StripPrefix=3
            - NoSQLSearchFilter
        - id: config-service-upsert
          uri: ${CONFIG_SERVICE_URL}
          predicates:
            - Path=/api/v1/config/upsert/**
          filters:
            - StripPrefix=3
            - UpsertFilter
        - id: config-service
          uri: ${CONFIG_SERVICE_URL}
          predicates:
            - Path=/api/v1/config/**
          filters:
            - RewritePath=/api/v1/config/(?<segment>.*), /$\{segment}
        - id: form-builder-service-search
          uri: ${FORM_BUILDER_SERVICE_URL}
          predicates:
            - Path=/api/v1/form-builder/search/**
          filters:
            - StripPrefix=3
            - NoSQLSearchFilter
        - id: form-builder-service-upsert
          uri: ${FORM_BUILDER_SERVICE_URL}
          predicates:
            - Path=/api/v1/form-builder/upsert/**
          filters:
            - StripPrefix=3
            - UpsertFilter
        - id: form-builder-service
          uri: ${FORM_BUILDER_SERVICE_URL}
          predicates:
            - Path=/api/v1/form-builder/**
          filters:
            - RewritePath=/api/v1/form-builder/(?<segment>.*), /$\{segment}
        - id: activity-service-search
          uri: ${ACTIVITY_SERVICE_URL}
          predicates:
            - Path=/api/v1/activity/search/**
          filters:
            - StripPrefix=3
            - SearchFilter
        - id: activity-service-upsert
          uri: ${ACTIVITY_SERVICE_URL}
          predicates:
            - Path=/api/v1/activity/upsert/**
          filters:
            - StripPrefix=3
            - UpsertFilter
        - id: activity-service
          uri: ${ACTIVITY_SERVICE_URL}
          predicates:
            - Path=/api/v1/activity/**
          filters:
            - RewritePath=/api/v1/activity/(?<segment>.*), /$\{segment}
        - id: entity-service-search
          uri: ${ENTITY_SERVICE_URL}
          predicates:
            - Path=/api/v1/entity/search/**
          filters:
            - StripPrefix=3
            - NoSQLSearchFilter
        - id: entity-service-upsert
          uri: ${ENTITY_SERVICE_URL}
          predicates:
            - Path=/api/v1/entity/upsert/**
          filters:
            - StripPrefix=3
            - UpsertFilter
        - id: entity-service
          uri: ${ENTITY_SERVICE_URL}
          predicates:
            - Path=/api/v1/entity/**
          filters:
            - RewritePath=/api/v1/entity/(?<segment>.*), /$\{segment}
        - id: feedback-service
          uri: ${FEEDBACK_SERVICE_URL}
          predicates:
            - Path=/api/v1/feedback/**
          filters:
            - RewritePath=/api/v1/feedback/(?<segment>.*), /$\{segment}




management:
  endpoints:
    web:
      exposure:
        include: "*" # Expose all actuator endpoints

app:
  auth:
    url: ${IAM_SERVICE_URL}


#"http://localhost:8080"


#https://iam-service-dev-v25.agric-os.com