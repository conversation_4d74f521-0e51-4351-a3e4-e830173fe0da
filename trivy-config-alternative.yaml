# Alternative Trivy configuration for non-blocking scans
# Replace the existing Trivy step with this configuration if you prefer

- name: Run Trivy vulnerability scanner (Non-blocking)
  uses: aquasecurity/trivy-action@0.28.0
  continue-on-error: true  # This makes the step non-blocking
  with:
    image-ref: '${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE }}:${{ steps.truncate_sha.outputs.short_sha }}'
    format: 'table'
    exit-code: '1'
    ignore-unfixed: true
    vuln-type: 'os,library'
    severity: 'CRITICAL,HIGH'
    trivyignores: '.trivyignore'

# Or completely change exit-code to 0 to never fail:
- name: Run Trivy vulnerability scanner (Report only)
  uses: aquasecurity/trivy-action@0.28.0
  with:
    image-ref: '${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE }}:${{ steps.truncate_sha.outputs.short_sha }}'
    format: 'table'
    exit-code: '0'  # Never fail the build
    ignore-unfixed: true
    vuln-type: 'os,library'
    severity: 'CRITICAL,HIGH,MEDIUM'
    trivyignores: '.trivyignore'
